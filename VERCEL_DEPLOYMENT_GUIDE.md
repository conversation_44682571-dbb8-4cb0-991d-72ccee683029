# Vercel Deployment Guide for IELTS Certification System

## Prerequisites

1. **Vercel Account**: Sign up at [vercel.com](https://vercel.com)
2. **GitHub Repository**: Push your code to GitHub
3. **PostgreSQL Database**: Set up a production database (recommended: Neon, Supabase, or Railway)

## Step 1: Database Setup

### Option A: Neon (Recommended)
1. Go to [neon.tech](https://neon.tech) and create an account
2. Create a new project
3. Copy the connection string (it should look like: `************************************************************`)

### Option B: Supabase
1. Go to [supabase.com](https://supabase.com) and create an account
2. Create a new project
3. Go to Settings > Database and copy the connection string

### Option C: Railway
1. Go to [railway.app](https://railway.app) and create an account
2. Create a new PostgreSQL service
3. Copy the connection string from the service details

## Step 2: Environment Variables

Set up the following environment variables in Vercel:

### Required Variables
```bash
# Authentication
NEXTAUTH_SECRET=your-super-secret-key-at-least-32-characters-long
NEXTAUTH_URL=https://your-app-name.vercel.app

# Database
DATABASE_URL=************************************************************

# Admin Credentials
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=your-secure-admin-password

# Public URL
NEXT_PUBLIC_APP_URL=https://your-app-name.vercel.app
```

### Optional Variables
```bash
# AI Integration (for feedback generation)
ANTHROPIC_API_KEY=your-anthropic-api-key
```

## Step 3: Deploy to Vercel

### Method 1: Vercel Dashboard
1. Go to [vercel.com/dashboard](https://vercel.com/dashboard)
2. Click "New Project"
3. Import your GitHub repository
4. Configure the project:
   - **Framework Preset**: Next.js
   - **Build Command**: `npm run build`
   - **Output Directory**: `.next`
   - **Install Command**: `npm ci`
   - **Development Command**: `npm run dev`

### Method 2: Vercel CLI
```bash
# Install Vercel CLI
npm i -g vercel

# Login to Vercel
vercel login

# Deploy
vercel --prod
```

## Step 4: Set Environment Variables in Vercel

1. Go to your project dashboard on Vercel
2. Click on "Settings" tab
3. Click on "Environment Variables"
4. Add each environment variable:
   - **Name**: Variable name (e.g., `NEXTAUTH_SECRET`)
   - **Value**: Variable value
   - **Environment**: Select "Production", "Preview", and "Development"

## Step 5: Database Migration

After deployment, you need to run database migrations:

### Option A: Using Vercel CLI
```bash
# Set environment variables locally for migration
export DATABASE_URL="your-production-database-url"

# Run migrations
npm run db:migrate

# Set up initial data
npm run db:setup
```

### Option B: Using Vercel Functions
1. Create a temporary API endpoint for migration
2. Visit the endpoint once to run migrations
3. Delete the endpoint after use

## Step 6: Verify Deployment

1. Visit your deployed app: `https://your-app-name.vercel.app`
2. Check the health endpoint: `https://your-app-name.vercel.app/api/health`
3. Try logging in with admin credentials
4. Test basic functionality

## Troubleshooting

### Common Issues

#### 1. "Server Error" on Login
- **Cause**: Database connection issues or missing environment variables
- **Solution**: Check environment variables and database connectivity

#### 2. "NEXTAUTH_URL" Error
- **Cause**: Incorrect NEXTAUTH_URL configuration
- **Solution**: Ensure NEXTAUTH_URL matches your Vercel domain exactly

#### 3. Database Connection Timeout
- **Cause**: Database not accessible or connection string incorrect
- **Solution**: Verify database URL and ensure database is running

#### 4. Build Failures
- **Cause**: Missing dependencies or TypeScript errors
- **Solution**: Run `npm run build` locally to identify issues

### Debug Steps

1. **Check Vercel Function Logs**:
   - Go to Vercel Dashboard > Your Project > Functions
   - Click on any function to see logs

2. **Test Health Endpoint**:
   ```bash
   curl https://your-app-name.vercel.app/api/health
   ```

3. **Check Environment Variables**:
   - Ensure all required variables are set
   - Verify no typos in variable names

4. **Database Connectivity**:
   - Test database connection from your local machine
   - Ensure database allows connections from Vercel IPs

## Performance Optimization

1. **Database Connection Pooling**: Already configured in the codebase
2. **Function Timeouts**: Configured in `vercel.json`
3. **Edge Regions**: Set to `iad1` for optimal performance

## Security Considerations

1. **Environment Variables**: Never commit sensitive data to Git
2. **Database Access**: Use connection pooling and SSL
3. **Authentication**: Strong NEXTAUTH_SECRET (32+ characters)
4. **Admin Credentials**: Use strong passwords

## Maintenance

1. **Regular Updates**: Keep dependencies updated
2. **Database Backups**: Set up automated backups
3. **Monitoring**: Use Vercel Analytics and monitoring tools
4. **Logs**: Regularly check function logs for errors

## Support

If you encounter issues:
1. Check Vercel documentation
2. Review function logs
3. Test locally first
4. Check database connectivity
5. Verify environment variables
