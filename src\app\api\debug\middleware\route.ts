import { NextResponse } from 'next/server';

/**
 * Debug endpoint to check middleware and auth configuration
 * This helps diagnose MIDDLEWARE_INVOCATION_FAILED errors
 */
export async function GET() {
  try {
    const diagnostics = {
      timestamp: new Date().toISOString(),
      environment: {
        NODE_ENV: process.env.NODE_ENV,
        NEXTAUTH_SECRET: !!process.env.NEXTAUTH_SECRET,
        NEXTAUTH_URL: process.env.NEXTAUTH_URL || 'not set',
        DATABASE_URL: !!process.env.DATABASE_URL,
        VERCEL: !!process.env.VERCEL,
        VERCEL_ENV: process.env.VERCEL_ENV || 'not set',
        VERCEL_URL: process.env.VERCEL_URL || 'not set',
      },
      nextauth: {
        secret_length: process.env.NEXTAUTH_SECRET?.length || 0,
        url_valid: process.env.NEXTAUTH_URL ? isValidUrl(process.env.NEXTAUTH_URL) : false,
      },
      database: {
        url_present: !!process.env.DATABASE_URL,
        url_format: process.env.DATABASE_URL ? 
          process.env.DATABASE_URL.startsWith('postgresql://') : false,
      },
      middleware: {
        config_present: true, // We know it exists since this endpoint works
        auth_import: 'attempting to test...',
      }
    };

    // Test auth import
    try {
      const { auth } = await import('@/lib/auth');
      diagnostics.middleware.auth_import = 'success';
      
      // Test if auth function is callable
      if (typeof auth === 'function') {
        diagnostics.middleware.auth_function = 'callable';
      } else {
        diagnostics.middleware.auth_function = 'not a function';
      }
    } catch (authError) {
      diagnostics.middleware.auth_import = `failed: ${authError instanceof Error ? authError.message : 'unknown error'}`;
    }

    // Test database connection
    try {
      const { db } = await import('@/lib/db');
      await db.execute('SELECT 1 as test');
      diagnostics.database.connection = 'success';
    } catch (dbError) {
      diagnostics.database.connection = `failed: ${dbError instanceof Error ? dbError.message : 'unknown error'}`;
    }

    return NextResponse.json({
      status: 'debug_complete',
      diagnostics,
      recommendations: generateRecommendations(diagnostics),
    });

  } catch (error) {
    console.error('Debug endpoint error:', error);
    return NextResponse.json(
      {
        status: 'debug_failed',
        error: error instanceof Error ? error.message : 'unknown error',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

function isValidUrl(url: string): boolean {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
}

function generateRecommendations(diagnostics: any): string[] {
  const recommendations: string[] = [];

  if (!diagnostics.environment.NEXTAUTH_SECRET) {
    recommendations.push('Set NEXTAUTH_SECRET environment variable');
  }

  if (diagnostics.nextauth.secret_length < 32) {
    recommendations.push('NEXTAUTH_SECRET should be at least 32 characters long');
  }

  if (!diagnostics.nextauth.url_valid) {
    recommendations.push('Set a valid NEXTAUTH_URL (e.g., https://your-app.vercel.app)');
  }

  if (!diagnostics.database.url_present) {
    recommendations.push('Set DATABASE_URL environment variable');
  }

  if (!diagnostics.database.url_format) {
    recommendations.push('DATABASE_URL should start with postgresql://');
  }

  if (diagnostics.middleware.auth_import !== 'success') {
    recommendations.push('Fix auth module import issues');
  }

  if (diagnostics.database.connection !== 'success') {
    recommendations.push('Fix database connection issues');
  }

  if (recommendations.length === 0) {
    recommendations.push('All checks passed - the issue might be intermittent or related to specific routes');
  }

  return recommendations;
}
