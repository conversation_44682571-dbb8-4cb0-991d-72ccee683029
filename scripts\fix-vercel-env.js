#!/usr/bin/env node

/**
 * <PERSON>ript to help fix Vercel environment variables for IELTS Certification System
 * Run this script to automatically set the correct environment variables
 */

const { execSync } = require('child_process');

console.log('🔧 IELTS Certification System - Vercel Environment Fix\n');

// Check if Vercel CLI is installed
try {
  execSync('vercel --version', { stdio: 'ignore' });
  console.log('✅ Vercel CLI is installed');
} catch (error) {
  console.log('❌ Vercel CLI not found. Installing...');
  try {
    execSync('npm install -g vercel', { stdio: 'inherit' });
    console.log('✅ Vercel CLI installed successfully');
  } catch (installError) {
    console.error('❌ Failed to install Vercel CLI. Please install manually:');
    console.error('   npm install -g vercel');
    process.exit(1);
  }
}

// Get project info
try {
  console.log('\n📋 Getting project information...');
  const projectInfo = execSync('vercel project ls --json', { encoding: 'utf8' });
  const projects = JSON.parse(projectInfo);
  
  const ieltsProject = projects.find(p => 
    p.name.toLowerCase().includes('ielts') || 
    p.name.toLowerCase().includes('certification')
  );
  
  if (ieltsProject) {
    console.log(`✅ Found project: ${ieltsProject.name}`);
    
    // Get deployment URL
    const deployments = execSync(`vercel ls ${ieltsProject.name} --json`, { encoding: 'utf8' });
    const deploymentList = JSON.parse(deployments);
    
    if (deploymentList.length > 0) {
      const latestDeployment = deploymentList[0];
      const appUrl = `https://${latestDeployment.url}`;
      
      console.log(`🌐 Latest deployment URL: ${appUrl}`);
      console.log('\n🔧 Setting environment variables...');
      
      // Set environment variables
      const envVars = [
        {
          name: 'NEXTAUTH_URL',
          value: appUrl,
          description: 'NextAuth URL for authentication'
        },
        {
          name: 'NEXTAUTH_SECRET',
          value: 'eb2Xhkj+y/rsEux1+v42VEJkHoB3Zm10mmeS73N9X3I=',
          description: 'NextAuth secret key'
        },
        {
          name: 'DATABASE_URL',
          value: 'postgresql://ielts-certification-system_owner:<EMAIL>/ielts-certification-system?sslmode=require',
          description: 'Database connection string'
        },
        {
          name: 'ADMIN_EMAIL',
          value: '<EMAIL>',
          description: 'Admin email'
        },
        {
          name: 'ADMIN_PASSWORD',
          value: 'admin123',
          description: 'Admin password'
        },
        {
          name: 'NODE_ENV',
          value: 'production',
          description: 'Node environment'
        }
      ];
      
      for (const envVar of envVars) {
        try {
          console.log(`Setting ${envVar.name}...`);
          execSync(`echo "${envVar.value}" | vercel env add ${envVar.name} production`, { 
            stdio: ['pipe', 'inherit', 'inherit'],
            input: envVar.value 
          });
          console.log(`✅ ${envVar.name} set successfully`);
        } catch (error) {
          console.log(`⚠️  ${envVar.name} might already exist or failed to set`);
        }
      }
      
      console.log('\n🚀 Triggering new deployment...');
      execSync('vercel --prod', { stdio: 'inherit' });
      
      console.log('\n✅ Environment variables updated and app redeployed!');
      console.log('\n🔍 Test your app:');
      console.log(`   App URL: ${appUrl}`);
      console.log(`   Health Check: ${appUrl}/api/health`);
      console.log(`   Debug Middleware: ${appUrl}/api/debug/middleware`);
      
    } else {
      console.log('❌ No deployments found for this project');
    }
  } else {
    console.log('❌ IELTS project not found. Please make sure you\'re in the correct directory and have deployed to Vercel.');
  }
  
} catch (error) {
  console.error('❌ Error getting project information:', error.message);
  console.log('\n📝 Manual steps:');
  console.log('1. Run: vercel login');
  console.log('2. Run: vercel link');
  console.log('3. Run this script again');
}

console.log('\n📚 If you still have issues, check the fix-middleware-error.md file for detailed instructions.');
